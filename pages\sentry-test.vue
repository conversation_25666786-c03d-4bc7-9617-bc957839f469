<template>
  <div class="sentry-test-page">
    <v-container>
      <v-row justify="center">
        <v-col cols="12" md="8">
          <v-card class="pa-6">
            <v-card-title class="text-h4 mb-4">
              Sentry Integration Test - langu-frontend-7b
            </v-card-title>
            
            <v-card-text>
              <div class="mb-4">
                <h3>Sentry Configuration Status</h3>
                <v-chip
                  :color="sentryStatus.shouldWork ? 'success' : 'error'"
                  text-color="white"
                  class="mb-2"
                >
                  {{ sentryStatus.shouldWork ? 'Active' : 'Inactive' }}
                </v-chip>

                <v-chip
                  v-if="!sentryStatus.isEnabled"
                  color="warning"
                  text-color="white"
                  class="mb-2 ml-2"
                >
                  Development Mode
                </v-chip>

                <div class="mt-2">
                  <strong>Project:</strong> langu-frontend-7b<br>
                  <strong>Environment:</strong> {{ sentryStatus.environment }}<br>
                  <strong>DSN:</strong> {{ sentryStatus.dsn ? 'Configured' : 'Not Set' }}<br>
                  <strong>Release:</strong> {{ sentryStatus.release }}<br>
                  <strong>Status:</strong> {{ sentryStatus.isEnabled ? 'Enabled' : 'Disabled (Dev Mode)' }}
                </div>
              </div>

              <v-divider class="my-4"></v-divider>

              <div class="mb-4">
                <h3>Test Sentry Integration</h3>
                <p class="text-body-2 mb-3">
                  Use these buttons to test different types of Sentry events:
                </p>
                
                <div class="d-flex flex-wrap gap-2">
                  <v-btn
                    color="info"
                    @click="testMessage"
                  >
                    Test Message
                  </v-btn>
                  
                  <v-btn
                    color="warning"
                    @click="testWarning"
                  >
                    Test Warning
                  </v-btn>
                  
                  <v-btn
                    color="error"
                    @click="testError"
                  >
                    Test Error
                  </v-btn>
                  
                  <v-btn
                    color="purple"
                    @click="testException"
                  >
                    Test Exception
                  </v-btn>
                </div>
              </div>

              <v-divider class="my-4"></v-divider>

              <div class="mb-4">
                <h3>Recent Test Results</h3>
                <v-list v-if="testResults.length > 0">
                  <v-list-item
                    v-for="(result, index) in testResults"
                    :key="index"
                  >
                    <v-list-item-content>
                      <v-list-item-title>{{ result.type }}</v-list-item-title>
                      <v-list-item-subtitle>
                        {{ result.message }} - {{ result.timestamp }}
                      </v-list-item-subtitle>
                    </v-list-item-content>
                    <v-list-item-action>
                      <v-chip
                        :color="result.success ? 'success' : 'error'"
                        small
                        text-color="white"
                      >
                        {{ result.success ? 'Sent' : 'Failed' }}
                      </v-chip>
                    </v-list-item-action>
                  </v-list-item>
                </v-list>
                <p v-else class="text-body-2 text--secondary">
                  No tests run yet. Click the buttons above to test Sentry integration.
                </p>
              </div>

              <v-alert
                v-if="!sentryStatus.isEnabled"
                type="warning"
                class="mt-4"
              >
                <strong>Development Mode:</strong>
                Sentry is disabled in development environment.
                Only staging and production environments will send events to Sentry.
              </v-alert>

              <v-alert
                v-else-if="sentryStatus.environment === 'staging'"
                type="info"
                class="mt-4"
              >
                <strong>Staging Mode:</strong>
                Sentry is active. All events are sent and logged to console for debugging.
                Use <code>window.sentryTest</code> helpers for testing.
              </v-alert>

              <v-alert
                v-else-if="sentryStatus.environment === 'production'"
                type="success"
                class="mt-4"
              >
                <strong>Production Mode:</strong>
                Sentry is fully active. All errors are tracked with 10% performance sampling.
              </v-alert>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<script>
export default {
  name: 'SentryTestPage',
  data() {
    return {
      testResults: [],
    }
  },
  head() {
    return {
      title: 'Sentry Test - langu-frontend-7b',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: 'Test page for Sentry integration in langu-frontend-7b',
        },
      ],
    }
  },
  computed: {
    sentryStatus() {
      const environment = process.env.NUXT_ENV_SENTRY_ENVIRONMENT
      return {
        isConfigured: !!this.$sentry,
        environment,
        dsn: !!process.env.NUXT_ENV_SENTRY_DSN,
        release: 'langu-frontend-7b@' + (process.env.npm_package_version || '1.0.0'),
        isEnabled: environment === 'staging' || environment === 'production',
        shouldWork: !!this.$sentry && !!process.env.NUXT_ENV_SENTRY_DSN,
      }
    },
  },
  methods: {
    addTestResult(type, message, success = true) {
      this.testResults.unshift({
        type,
        message,
        success,
        timestamp: new Date().toLocaleTimeString(),
      })
      
      // Keep only last 10 results
      if (this.testResults.length > 10) {
        this.testResults = this.testResults.slice(0, 10)
      }
    },
    
    testMessage() {
      try {
        if (this.$sentry) {
          this.$sentry.captureMessage('Test info message from langu-frontend-7b', 'info')
          this.addTestResult('Info Message', 'Test message sent successfully')
        } else {
          this.addTestResult('Info Message', 'Sentry not available', false)
        }
      } catch (error) {
        this.addTestResult('Info Message', `Error: ${error.message}`, false)
      }
    },
    
    testWarning() {
      try {
        if (this.$sentry) {
          this.$sentry.captureMessage('Test warning from langu-frontend-7b', 'warning')
          this.addTestResult('Warning', 'Warning message sent successfully')
        } else {
          this.addTestResult('Warning', 'Sentry not available', false)
        }
      } catch (error) {
        this.addTestResult('Warning', `Error: ${error.message}`, false)
      }
    },
    
    testError() {
      try {
        if (this.$sentry) {
          this.$sentry.captureMessage('Test error from langu-frontend-7b', 'error')
          this.addTestResult('Error Message', 'Error message sent successfully')
        } else {
          this.addTestResult('Error Message', 'Sentry not available', false)
        }
      } catch (error) {
        this.addTestResult('Error Message', `Error: ${error.message}`, false)
      }
    },
    
    testException() {
      try {
        if (this.$sentry) {
          const testError = new Error('Test exception from langu-frontend-7b')
          testError.name = 'TestError'
          this.$sentry.captureException(testError)
          this.addTestResult('Exception', 'Exception sent successfully')
        } else {
          this.addTestResult('Exception', 'Sentry not available', false)
        }
      } catch (error) {
        this.addTestResult('Exception', `Error: ${error.message}`, false)
      }
    },
  },
}
</script>

<style scoped>
.sentry-test-page {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20px 0;
}

.gap-2 > * {
  margin-right: 8px;
  margin-bottom: 8px;
}
</style>
